@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 240 33% 5%;
  --foreground: 0 0% 100%;
  --primary: 270 86% 53%;
  --secondary: 171 60% 43%;
  --accent: 224 65% 76%;
  --muted: 0 0% 16%;
  --muted-foreground: 220 14% 91%;
  --border: 0 0% 16%;
}

body {
  background-color: #050816;
  color: #ffffff;
}

.gradient-progress {
  background: linear-gradient(261.81deg, #8c01fa -8.01%, #19fb9b 100%);
  border-radius: 80px;
}

.normal-progress {
  background: linear-gradient(0deg, #B9BCBE, #B9BCBE);
  border-radius: 80px;
}

.gradient-text {
  background: linear-gradient(263.61deg, #19fb9b 25.9%, #8c01fa 79.13%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.gradient-button {
  background: linear-gradient(90deg, #7f20ef 0%, #2cad9c 100%);
}

.prediction-button {
  background: linear-gradient(261.81deg, #19fb9b -8.01%, #8c01fa 100%);
}

.border-gradient {
  border-image: linear-gradient(90deg, #7f20ef, #2cad9c) 1;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(-8px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes menuBounce {
  0% {
    transform: scale(0.95) translateY(-4px);
  }

  50% {
    transform: scale(1.02) translateY(0);
  }

  100% {
    transform: scale(1) translateY(0);
  }
}

.menu-open {
  animation: menuBounce 0.4s ease-out forwards;
}

/* Animated Background Blurs */
@keyframes floatBlur1 {
  0% {
    transform: rotate(-164.56deg) translate(0, 0);
  }
  25% {
    transform: rotate(-164.56deg) translate(30px, -20px);
  }
  50% {
    transform: rotate(-164.56deg) translate(-20px, 30px);
  }
  75% {
    transform: rotate(-164.56deg) translate(40px, 10px);
  }
  100% {
    transform: rotate(-164.56deg) translate(0, 0);
  }
}

@keyframes floatBlur2 {
  0% {
    transform: rotate(-164.56deg) translate(0, 0);
  }
  25% {
    transform: rotate(-164.56deg) translate(-40px, 20px);
  }
  50% {
    transform: rotate(-164.56deg) translate(25px, -30px);
  }
  75% {
    transform: rotate(-164.56deg) translate(-15px, -10px);
  }
  100% {
    transform: rotate(-164.56deg) translate(0, 0);
  }
}

/* Gradient Text Hover Effects */
.gradient-text {
  background: linear-gradient(263.61deg, #19fb9b 25.9%, #8c01fa 79.13%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  transition: all 0.3s ease;
  position: relative;
}

.gradient-text:hover {
  background: linear-gradient(263.61deg, #19fb9b 0%, #8c01fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: scale(1.02);
  filter: brightness(1.2);
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text-animated {
  background: linear-gradient(45deg, #19fb9b, #8c01fa, #19fb9b, #8c01fa);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease infinite;
}



/* Main Logo Styling */
.logo-container {
  position: relative;
  display: inline-block;
  perspective: 1000px;
}

.logo-main {
  filter: brightness(1) saturate(1);
  position: relative;
  z-index: 1;
  transform-style: preserve-3d;
  animation: logoTwirl 5s ease-in-out infinite;
}

/* Enhanced Logo Glow Animation */
@keyframes logoGlow {
  0% {
    filter: brightness(1.2) drop-shadow(0 0 10px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 20px rgba(255, 255, 255, 0.4));
  }
  25% {
    filter: brightness(1.4) drop-shadow(0 0 25px rgba(25, 251, 155, 1)) drop-shadow(0 0 40px rgba(25, 251, 155, 0.8)) drop-shadow(0 0 60px rgba(25, 251, 155, 0.4));
  }
  50% {
    filter: brightness(1.3) drop-shadow(0 0 30px rgba(140, 1, 250, 1)) drop-shadow(0 0 50px rgba(140, 1, 250, 0.8)) drop-shadow(0 0 80px rgba(140, 1, 250, 0.4));
  }
  75% {
    filter: brightness(1.4) drop-shadow(0 0 25px rgba(25, 251, 155, 1)) drop-shadow(0 0 40px rgba(25, 251, 155, 0.8)) drop-shadow(0 0 60px rgba(25, 251, 155, 0.4));
  }
  100% {
    filter: brightness(1.2) drop-shadow(0 0 10px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 20px rgba(255, 255, 255, 0.4));
  }
}

@keyframes logoFloat {
  0% {
    transform: translateY(0px) scale(1) rotate(0deg);
  }
  25% {
    transform: translateY(-8px) scale(1.05) rotate(1deg);
  }
  50% {
    transform: translateY(-12px) scale(1.08) rotate(0deg);
  }
  75% {
    transform: translateY(-8px) scale(1.05) rotate(-1deg);
  }
  100% {
    transform: translateY(0px) scale(1) rotate(0deg);
  }
}

@keyframes logoPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes logoTwirl {
  0% {
    transform: rotateY(0deg);
  }
  25% {
    transform: rotateY(90deg);
  }
  50% {
    transform: rotateY(180deg);
  }
  75% {
    transform: rotateY(270deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.logo-glow-animation {
  animation: logoGlow 2.5s ease-in-out infinite, logoFloat 3s ease-in-out infinite, logoPulse 4s ease-in-out infinite;
}

/* Animated Ring Around Logo */
@keyframes logoRing {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.3;
  }
}

.logo-ring-animation {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, #19FB9B, #8C01FA, #19FB9B) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  animation: logoRing 3s linear infinite;
  z-index: 1;
}



/* Enhanced Button Hover Effects */
@keyframes buttonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 251, 155, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(25, 251, 155, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 251, 155, 0);
  }
}

@keyframes gradientShimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Advanced Particle System */
@keyframes particleFloat {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes particleGlow {
  0%, 100% {
    filter: brightness(1) blur(0px);
  }
  50% {
    filter: brightness(1.5) blur(1px);
  }
}

.particle {
  position: absolute;
  pointer-events: none;
  border-radius: 50%;
  animation: particleFloat linear infinite, particleGlow 3s ease-in-out infinite;
}

.particle-1 {
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #19FB9B, #8C01FA);
  animation-duration: 15s, 3s;
  animation-delay: 0s, 0s;
}

.particle-2 {
  width: 6px;
  height: 6px;
  background: linear-gradient(45deg, #8C01FA, #19FB9B);
  animation-duration: 20s, 4s;
  animation-delay: 2s, 1s;
}

.particle-3 {
  width: 3px;
  height: 3px;
  background: linear-gradient(45deg, #19FB9B, #2CAD9C);
  animation-duration: 18s, 2.5s;
  animation-delay: 4s, 0.5s;
}

/* Floating Geometric Shapes */
@keyframes geometricFloat {
  0% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  33% {
    transform: translateY(-20px) rotate(120deg) scale(1.1);
  }
  66% {
    transform: translateY(10px) rotate(240deg) scale(0.9);
  }
  100% {
    transform: translateY(0px) rotate(360deg) scale(1);
  }
}

.geometric-shape {
  position: absolute;
  pointer-events: none;
  opacity: 0.1;
  animation: geometricFloat 20s ease-in-out infinite;
}

.geometric-triangle {
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 26px solid #19FB9B;
  animation-delay: 0s;
}

.geometric-square {
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, #8C01FA, #19FB9B);
  animation-delay: 5s;
}

.geometric-circle {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: linear-gradient(45deg, #2CAD9C, #7F20EF);
  animation-delay: 10s;
}

/* 3D Card Hover Effects */
.card-3d {
  perspective: 1000px;
  transition: all 0.3s ease;
}

.card-3d-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.card-3d:hover .card-3d-inner {
  transform: rotateY(5deg) rotateX(5deg) translateZ(20px);
}

.card-3d:hover {
  box-shadow:
    0 20px 40px rgba(25, 251, 155, 0.2),
    0 0 0 1px rgba(25, 251, 155, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Magnetic Button Effect */
.magnetic-button {
  position: relative;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
  cursor: pointer;
}

.magnetic-button:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(25, 251, 155, 0.3),
    0 0 0 1px rgba(25, 251, 155, 0.2);
}

.magnetic-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background: linear-gradient(45deg, #19FB9B, #8C01FA, #19FB9B, #8C01FA);
  background-size: 400% 400%;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: gradientShift 3s ease infinite;
  z-index: -1;
}

.magnetic-button:hover::before {
  opacity: 0.8;
}

/* Liquid Button Effect */
@keyframes liquidWave {
  0% {
    transform: translateX(-100%) skewX(45deg);
  }
  100% {
    transform: translateX(200%) skewX(45deg);
  }
}

.liquid-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(90deg, #8C01FA 0%, #19FB9B 100%);
  transition: all 0.3s ease;
}

.liquid-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: translateX(-100%) skewX(45deg);
  transition: transform 0.6s;
}

.liquid-button:hover::before {
  animation: liquidWave 0.6s ease-in-out;
}

.liquid-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(25, 251, 155, 0.4);
}

/* Text Reveal Animation */
@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.text-reveal {
  animation: textReveal 0.8s ease-out forwards;
}

.text-reveal-delay-1 { animation-delay: 0.1s; }
.text-reveal-delay-2 { animation-delay: 0.2s; }
.text-reveal-delay-3 { animation-delay: 0.3s; }
.text-reveal-delay-4 { animation-delay: 0.4s; }

.gradient-button-enhanced {
  background: linear-gradient(90deg, #8C01FA 0%, #19FB9B 100%);
  background-size: 200% 100%;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.gradient-button-enhanced:hover {
  transform: translateY(-2px);
  animation: buttonPulse 1.5s infinite, gradientShimmer 2s ease-in-out infinite;
  filter: brightness(1.1);
}

.gradient-button-enhanced:active {
  transform: translateY(0);
}

.gradient-button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.gradient-button-enhanced:hover::before {
  left: 100%;
}

/* Prediction Button Specific Styling */
.prediction-button {
  background: linear-gradient(261.81deg, #19fb9b -8.01%, #8c01fa 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.prediction-button:hover {
  transform: translateY(-2px);
  filter: brightness(1.15);
  box-shadow: 0 8px 25px rgba(25, 251, 155, 0.3);
}

.prediction-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.prediction-button:hover::after {
  width: 300px;
  height: 300px;
}

.tab-button {
  position: relative;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 700;
  color: #9CA2B5;
  background: transparent;
  width: 100%;
}

.tab-button.active {
  color: white;
}

.tab-button.active::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  background: linear-gradient(0deg, rgba(11, 10, 10, 0.4), rgba(0, 0, 0, 0.4)),
    linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  padding: 1.5px;
  background: linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

@media (min-width: 768px) {
  .tab-button-container {
    display: none;
  }

  .tab-content {
    display: block;
  }
}

@media (max-height: 300px) {
  .h-full {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  button,
  .icon {
    transform: scale(0.8);
  }

  .gap-4 {
    gap: 0.5rem !important;
  }

  .ScrollArea {
    max-height: 150px !important;
  }

  header,
  .header {
    min-height: 40px !important;
  }
}

/* Parallax Scroll Effects */
@keyframes parallaxFloat {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  50% {
    transform: translateY(-10px) translateX(5px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

.parallax-element {
  animation: parallaxFloat 6s ease-in-out infinite;
}

.parallax-slow { animation-duration: 8s; }
.parallax-fast { animation-duration: 4s; }

/* Header Link Hover Effects */
@keyframes linkGlow {
  0% {
    text-shadow: 0 0 5px rgba(25, 251, 155, 0.3);
  }
  50% {
    text-shadow: 0 0 15px rgba(25, 251, 155, 0.8), 0 0 25px rgba(140, 1, 250, 0.6);
  }
  100% {
    text-shadow: 0 0 5px rgba(25, 251, 155, 0.3);
  }
}

@keyframes linkUnderline {
  0% {
    width: 0%;
    opacity: 0;
  }
  100% {
    width: 100%;
    opacity: 1;
  }
}

/* Navbar Link Hover Effects */
.nav-link-hover {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link-hover::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0%;
  height: 2px;
  background: linear-gradient(90deg, #19FB9B, #8C01FA);
  transition: width 0.3s ease;
}

.nav-link-hover:hover {
  color: #19FB9B;
  animation: linkGlow 1.5s ease-in-out infinite;
  transform: translateY(-1px);
}

.nav-link-hover:hover::after {
  width: 100%;
}

/* Logo Hover Effects */
.logo-hover {
  transition: all 0.3s ease;
}

.logo-hover:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 0 10px rgba(25, 251, 155, 0.6));
}

/* Credits Badge Hover Effects */
.credits-hover {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.credits-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.credits-hover:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.credits-hover:hover::before {
  left: 100%;
}

/* Profile Avatar Hover Effects */
.profile-hover {
  transition: all 0.3s ease;
}

.profile-hover:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(25, 251, 155, 0.4);
}

/* Dramatic Login Button Hover Effects */
@keyframes loginButtonExplosion {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 251, 155, 0.8), 0 0 0 0 rgba(140, 1, 250, 0.8);
  }
  50% {
    box-shadow: 0 0 30px 10px rgba(25, 251, 155, 0.4), 0 0 40px 15px rgba(140, 1, 250, 0.4);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 251, 155, 0), 0 0 0 0 rgba(140, 1, 250, 0);
  }
}

@keyframes loginButtonRainbow {
  0% {
    background: linear-gradient(45deg, #19FB9B, #8C01FA);
  }
  16% {
    background: linear-gradient(45deg, #8C01FA, #FF6B6B);
  }
  33% {
    background: linear-gradient(45deg, #FF6B6B, #FFD93D);
  }
  50% {
    background: linear-gradient(45deg, #FFD93D, #6BCF7F);
  }
  66% {
    background: linear-gradient(45deg, #6BCF7F, #4D96FF);
  }
  83% {
    background: linear-gradient(45deg, #4D96FF, #9B59B6);
  }
  100% {
    background: linear-gradient(45deg, #9B59B6, #19FB9B);
  }
}

@keyframes loginButtonShake {
  0%, 100% { transform: translateX(0) scale(1); }
  10% { transform: translateX(-2px) scale(1.05); }
  20% { transform: translateX(2px) scale(1.05); }
  30% { transform: translateX(-2px) scale(1.05); }
  40% { transform: translateX(2px) scale(1.05); }
  50% { transform: translateX(-1px) scale(1.05); }
  60% { transform: translateX(1px) scale(1.05); }
  70% { transform: translateX(-1px) scale(1.05); }
  80% { transform: translateX(1px) scale(1.05); }
  90% { transform: translateX(0) scale(1.05); }
}

.login-button-dramatic {
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
}

.login-button-dramatic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.login-button-dramatic:hover {
  animation: loginButtonExplosion 1s ease-out infinite, loginButtonRainbow 2s linear infinite, loginButtonShake 0.5s ease-in-out infinite;
  transform: translateY(-3px) scale(1.1);
  filter: brightness(1.3) saturate(1.5);
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.login-button-dramatic:hover::before {
  left: 100%;
  animation: none;
}

/* Sign Up Button Enhanced Hover */
.signup-button-enhanced:hover {
  transform: translateY(-2px) scale(1.05);
  filter: brightness(1.2);
  box-shadow: 0 10px 25px rgba(25, 251, 155, 0.3);
}

/* Morphing Shapes */
@keyframes morphShape {
  0% {
    border-radius: 50%;
    transform: rotate(0deg) scale(1);
  }
  25% {
    border-radius: 25%;
    transform: rotate(90deg) scale(1.1);
  }
  50% {
    border-radius: 0%;
    transform: rotate(180deg) scale(0.9);
  }
  75% {
    border-radius: 25%;
    transform: rotate(270deg) scale(1.1);
  }
  100% {
    border-radius: 50%;
    transform: rotate(360deg) scale(1);
  }
}

.morph-shape {
  animation: morphShape 8s ease-in-out infinite;
}

/* Ripple Effect */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  background: rgba(25, 251, 155, 0.6);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

/* Glow Pulse Effect */
@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(25, 251, 155, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(25, 251, 155, 0.8), 0 0 30px rgba(140, 1, 250, 0.6);
  }
}

.glow-pulse {
  animation: glowPulse 2s ease-in-out infinite;
}

/* Typewriter Effect */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blinkCursor {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: #19FB9B;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid #19FB9B;
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blinkCursor 0.75s step-end infinite;
}

/* Floating Action Button */
@keyframes fabPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(25, 251, 155, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(25, 251, 155, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(25, 251, 155, 0);
  }
}

.fab-pulse {
  animation: fabPulse 2s infinite;
}

/* Interactive Hover Zones */
.hover-zone {
  position: relative;
  transition: all 0.3s ease;
}

.hover-zone::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(45deg, rgba(25, 251, 155, 0.1), rgba(140, 1, 250, 0.1));
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.hover-zone:hover::before {
  opacity: 1;
}

.hover-zone:hover {
  transform: translateY(-5px);
}

/* Scroll Reveal Animations */
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.slide-in-right {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.slide-in-bottom {
  animation: slideInFromBottom 0.8s ease-out forwards;
}

/* Enhanced Gradient Backgrounds */
@keyframes gradientWave {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-wave {
  background: linear-gradient(-45deg, #8C01FA, #19FB9B, #2CAD9C, #7F20EF);
  background-size: 400% 400%;
  animation: gradientWave 15s ease infinite;
}

@media (max-width: 400px) {

  .px-3,
  .px-4 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .gap-4,
  .gap-3 {
    gap: 0.5rem !important;
  }

  .min-w-320px {
    min-width: calc(100% - 0.5rem) !important;
  }

  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr)) !important;
  }

  .text-sm {
    font-size: 0.75rem !important;
  }

  button {
    padding: 0.25rem 0.5rem !important;
  }
}