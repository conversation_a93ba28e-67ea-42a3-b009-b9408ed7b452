"use client"

import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Menu } from "lucide-react"
import { useState, useEffect } from "react"
import { usePathname } from "next/navigation"

export default function Navbar() {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  const handleLinkClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    setIsOpen(false);

    const targetId = href.split('/').pop() || '';

    if (pathname === '/') {
      const element = document.getElementById(targetId);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      } else {
        window.location.href = href;
      }
    } else {
      if (href === '/about' || href === '/support' || href === '/signup') {
        window.location.href = href;
      } else {
        window.location.href = `/${targetId}`;
      }
    }
  }

  const navLinks = [
    { href: "/about", label: "About us" },
    { href: "/#how-it-works", label: "How It Works", isSection: true },
    { href: "/#features", label: "Features", isSection: true },
    { href: "/about#contacts", label: "Contacts", isSection: true },
    { href: "/support", label: "Support" }
  ]

  return (
    <nav className="w-full pb-4 pt-8 relative">
      <div className="container mx-auto flex items-center justify-between px-8">
        <Link href="/" className="flex items-center logo-hover">
          <div
            className="flex items-center rounded-[100px] w-[188px] h-[40px] px-6 relative"
            style={{
              gap: "5px",
              background:
                "linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)",
            }}
          >
            <div
              style={{
                position: "absolute",
                top: 0,
                right: 0,
                bottom: 0,
                left: 0,
                borderRadius: "100px",
                padding: "1.5px",
                background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                WebkitMaskComposite: "xor",
                maskComposite: "exclude",
                pointerEvents: "none",
              }}
            />
            <Image src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" alt="DarkHorse Icon" width={26} height={26} />
            <Image src="/DARKHORSE.svg" alt="DarkHorse" width={120} height={20} />
          </div>
        </Link>

        <div className="hidden xl:flex items-center space-x-8 pl-16">
          {navLinks.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="text-foreground hover:text-accent transition-colors text-[14px] relative pb-1 nav-link-hover"
              onClick={(e) => handleLinkClick(e, item.href)}
            >
              <span className={`relative ${pathname === item.href
                  ? 'bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] bg-clip-text text-transparent'
                  : 'text-foreground'
                }`}>
                {item.label}
                {pathname === item.href && (
                  <span
                    className="absolute bottom-0 left-0 w-full h-[1px]"
                    style={{
                      background: "linear-gradient(271.14deg, #19FB9B -34.76%, #8C01FA 224.83%)",
                      borderImageSource: "linear-gradient(271.14deg, #19FB9B -34.76%, #8C01FA 224.83%)",
                      borderImageSlice: "1",
                    }}
                  ></span>
                )}
              </span>
            </Link>
          ))}
        </div>

        <div className="hidden xl:flex items-center space-x-4">
          <Link href="/signup">
            <button className="gradient-button-enhanced signup-button-enhanced text-white text-[14px] font-medium rounded-full px-4 py-2 sm:px-6 sm:py-3 md:px-8 md:py-4 transition-all duration-300">
              Sign Up
            </button>
          </Link>
          <Link href="/login">
            <Button
              className="bg-white text-background hover:bg-muted-foreground rounded-full text-[14px] px-4 py-2 sm:px-6 sm:py-3 md:px-8 md:py-4 login-button-dramatic"
            >
              Login
            </Button>
          </Link>
        </div>

        <button
          className="xl:hidden text-white p-3 sm:p-4 hover:bg-white/10 rounded-full transition-all duration-200"
          onClick={() => setIsOpen(!isOpen)}
          aria-expanded={isOpen}
        >
          <Menu
            size={24}
            className={`transition-transform duration-300 ease-in-out ${isOpen ? 'rotate-180' : ''
              }`}
          />
        </button>

        <div
          className={`absolute z-50 top-full left-0 right-0 mt-2 xl:hidden`}
        >
          <div
            className={`bg-background/95 backdrop-blur-sm border border-border/50 rounded-2xl shadow-lg mx-4 transform transition-all duration-300 ease-in-out origin-top ${isOpen
              ? 'opacity-100 scale-100 translate-y-0'
              : 'opacity-0 scale-95 -translate-y-4 pointer-events-none'
              }`}
          >
            <div className="p-6">
              <div className="flex flex-col space-y-6">
                {navLinks.map((item, index) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`text-foreground/90 hover:text-accent transition-all duration-200 text-lg group flex items-center nav-link-hover ${pathname === item.href ? 'text-accent' : ''
                      }`}
                    onClick={(e) => handleLinkClick(e, item.href)}
                    style={{
                      animationName: isOpen ? 'slideIn' : 'none',
                      animationDuration: '0.4s',
                      animationTimingFunction: 'ease-out',
                      animationFillMode: 'forwards',
                      animationDelay: `${index * 60}ms`
                    }}
                  >
                    <span className="relative">
                      {item.label}
                      <span className="absolute -bottom-1 left-0 w-0 h-[2px] bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] transition-all duration-300 group-hover:w-full" />
                    </span>
                  </Link>
                ))}
              </div>

              <div className="mt-8 pt-6 border-t border-border/50 space-y-4">
                <Link
                  href="/login"
                  className="block text-foreground/90 hover:text-accent transition-all duration-200 text-[18px] sm:text-lg"
                  onClick={() => setIsOpen(false)}
                  style={{
                    animationName: isOpen ? 'slideIn' : 'none',
                    animationDuration: '0.4s',
                    animationTimingFunction: 'ease-out',
                    animationFillMode: 'forwards',
                    animationDelay: `${navLinks.length * 60}ms`
                  }}
                >
                  <span className="relative">
                    Login
                    <span className="absolute -bottom-1 left-0 w-0 h-[2px] bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] transition-all duration-300 group-hover:w-full" />
                  </span>
                </Link>
                <Link
                  href="/signup"
                  className="block text-foreground/90 hover:text-accent transition-all duration-200 text-[18px] sm:text-lg"
                  onClick={() => setIsOpen(false)}
                  style={{
                    animationName: isOpen ? 'slideIn' : 'none',
                    animationDuration: '0.4s',
                    animationTimingFunction: 'ease-out',
                    animationFillMode: 'forwards',
                    animationDelay: `${navLinks.length * 60}ms`
                  }}
                >
                  <span className="relative">
                    Sign Up
                    <span className="absolute -bottom-1 left-0 w-0 h-[2px] bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] transition-all duration-300 group-hover:w-full" />
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}





















