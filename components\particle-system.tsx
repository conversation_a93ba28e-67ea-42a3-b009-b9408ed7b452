"use client"

import { useEffect, useRef } from 'react'

interface Particle {
  x: number
  y: number
  vx: number
  vy: number
  size: number
  opacity: number
  color: string
  life: number
  maxLife: number
}

export default function ParticleSystem() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const particlesRef = useRef<Particle[]>([])
  const mouseRef = useRef({ x: 0, y: 0 })
  const animationRef = useRef<number>()

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }

    const createParticle = (x?: number, y?: number): Particle => {
      const colors = ['#19FB9B', '#8C01FA', '#2CAD9C', '#7F20EF']
      return {
        x: x ?? Math.random() * canvas.width,
        y: y ?? canvas.height + 10,
        vx: (Math.random() - 0.5) * 2,
        vy: -Math.random() * 3 - 1,
        size: Math.random() * 4 + 1,
        opacity: Math.random() * 0.8 + 0.2,
        color: colors[Math.floor(Math.random() * colors.length)],
        life: 0,
        maxLife: Math.random() * 200 + 100
      }
    }

    const updateParticle = (particle: Particle) => {
      particle.x += particle.vx
      particle.y += particle.vy
      particle.life++

      // Fade out as particle ages
      particle.opacity = Math.max(0, 1 - (particle.life / particle.maxLife))

      // Add some gravity
      particle.vy += 0.01

      // Mouse interaction
      const dx = mouseRef.current.x - particle.x
      const dy = mouseRef.current.y - particle.y
      const distance = Math.sqrt(dx * dx + dy * dy)

      if (distance < 100) {
        const force = (100 - distance) / 100
        particle.vx += (dx / distance) * force * 0.1
        particle.vy += (dy / distance) * force * 0.1
      }
    }

    const drawParticle = (particle: Particle) => {
      ctx.save()
      ctx.globalAlpha = particle.opacity
      ctx.fillStyle = particle.color
      ctx.shadowBlur = 10
      ctx.shadowColor = particle.color
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      ctx.fill()
      ctx.restore()
    }

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Add new particles
      if (Math.random() < 0.3) {
        particlesRef.current.push(createParticle())
      }

      // Update and draw particles
      particlesRef.current = particlesRef.current.filter(particle => {
        updateParticle(particle)
        drawParticle(particle)

        // Remove dead particles
        return particle.life < particle.maxLife &&
               particle.y > -10 &&
               particle.x > -10 &&
               particle.x < canvas.width + 10
      })

      animationRef.current = requestAnimationFrame(animate)
    }

    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current.x = e.clientX
      mouseRef.current.y = e.clientY
    }

    const handleClick = (e: MouseEvent) => {
      // Create burst of particles on click
      for (let i = 0; i < 10; i++) {
        particlesRef.current.push(createParticle(e.clientX, e.clientY))
      }
    }

    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)
    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('click', handleClick)

    animate()

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('click', handleClick)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  return (
    <>
      <canvas
        ref={canvasRef}
        className="fixed top-0 left-0 w-full h-full pointer-events-none z-10"
        style={{ mixBlendMode: 'screen' }}
      />

      {/* Static floating particles for fallback */}
      <div className="fixed inset-0 pointer-events-none z-5">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className={`particle particle-${(i % 3) + 1}`}
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 15}s`,
            }}
          />
        ))}
      </div>

      {/* Floating tiny dark horse logos */}
      <div className="fixed inset-0 pointer-events-none z-5">
        {Array.from({ length: 12 }).map((_, i) => (
          <div
            key={`horse-${i}`}
            className="horse-logo-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 25}s`,
              animationDuration: `${15 + Math.random() * 10}s`,
            }}
          >
            <svg
              width="16"
              height="18"
              viewBox="0 0 25 26"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="horse-icon"
            >
              <path
                d="M13.5225 8.98051C13.493 9.38546 13.4974 9.79483 13.425 10.1917C13.1952 11.4588 12.3256 12.3269 11.0608 12.3615C9.87721 12.3924 9.11401 13.0971 8.31979 13.7914C8.18015 13.9129 8.08189 14.1175 8.03239 14.3016C7.87798 14.8759 7.50561 15.152 6.93673 15.1748C6.55466 15.1885 6.17224 15.1897 5.79009 15.1785C5.45762 15.169 5.2478 14.9702 5.11998 14.6742C4.84884 14.0461 4.86878 13.3901 5.11259 12.7863C5.47354 11.9053 5.88348 11.045 6.3405 10.2094C6.90703 9.17172 7.59661 8.20566 8.39441 7.33197C8.18532 6.92702 8.35599 6.58833 8.57468 6.26215C8.6855 6.09723 8.79189 5.91905 8.94039 5.79682C9.28985 5.5082 9.27582 5.1489 9.23001 4.75278C9.18346 4.36328 9.17903 3.96937 9.15317 3.51067C9.77525 3.69474 10.1018 4.16228 10.5 4.52674L10.5843 4.48992V3.06301C10.7911 3.15799 10.9315 3.19628 11.0423 3.27727C11.5927 3.6675 11.9658 4.20498 12.2148 4.82346C12.3493 5.15479 12.4387 5.50599 12.5465 5.84836C12.585 5.97279 12.6204 6.10017 12.6559 6.22534H12.7313L12.896 5.49642C13.0334 5.5325 13.1398 5.54501 13.2366 5.58477C14.6825 6.19147 16.0035 6.99623 17.1804 8.0388C19.5572 10.1431 20.8789 12.8276 21.4522 15.917C21.5557 16.4736 21.5586 17.0509 21.6148 17.6171C21.6517 18.0095 21.5158 18.3335 21.2971 18.656C19.5616 21.2057 17.3592 23.2482 14.7194 24.8165C14.2 25.1257 13.6555 25.3937 13.1339 25.6971C12.8701 25.8509 12.6374 25.834 12.3692 25.7007C10.6706 24.8647 9.08787 23.8126 7.66077 22.5708C5.75126 20.9057 4.17976 18.8912 3.03135 16.6364C2.09601 14.8133 1.5153 12.8791 1.18505 10.8566C0.876966 8.97093 0.94346 7.07354 0.981878 5.17835C0.990744 4.73658 1.06019 4.29481 1.10674 3.80886C1.44438 3.75364 1.75394 3.68664 2.0672 3.65572C2.73952 3.59019 3.41553 3.53938 4.09007 3.49152C4.46317 3.46502 4.47056 3.46502 4.52006 3.08804C4.56661 2.73315 4.62645 2.37753 4.66487 2.02043C4.69147 1.7804 4.80672 1.64272 5.03206 1.56394C6.34613 1.10261 7.69573 0.748764 9.06747 0.505901C10.598 0.242151 12.1527 0.144882 13.7043 0.215806C16.0471 0.313731 18.3189 0.793051 20.5258 1.58529C20.7075 1.65008 20.8065 1.74801 20.8264 1.95564C20.8604 2.32525 20.9203 2.69192 20.9727 3.06006C21.0222 3.41348 21.0895 3.45766 21.4485 3.48711C22.307 3.55632 23.1641 3.63878 24.0211 3.72051C24.1408 3.73155 24.2582 3.77647 24.409 3.81549C24.839 7.95781 24.5649 11.9919 22.8589 15.8368C22.8205 15.8412 22.7836 15.8478 22.7452 15.8515C22.703 15.7374 22.6491 15.6255 22.6232 15.5076C22.2258 13.7752 21.5704 12.1531 20.607 10.6592C19.091 8.30828 17.0119 6.63618 14.4704 5.52955C13.9747 5.31235 13.4582 5.13859 12.9529 4.94421C12.6426 4.82493 12.5761 4.74762 12.5059 4.38905C14.449 4.37212 16.3404 4.63718 18.1926 5.17098L18.2044 5.28584C18.078 5.34666 17.9512 5.40655 17.8239 5.4655C17.6931 5.5244 17.5601 5.58036 17.374 5.66282C18.146 6.08471 18.8612 6.458 19.5594 6.86296C20.2517 7.26497 20.8952 7.73472 21.4596 8.35025H20.556C21.1663 9.14543 21.7234 9.87656 22.2819 10.6055L22.3588 10.5908C22.5952 8.93486 22.7304 7.27233 22.5316 5.56637C21.4589 5.33076 20.3662 5.44267 19.272 5.27185C19.2099 4.73216 19.139 4.20793 19.0932 3.68075C19.0489 3.17787 19.0466 3.15136 18.5708 3.00558C17.0696 2.5454 15.538 2.2693 13.9688 2.15444C11.6016 1.98067 9.30685 2.33262 7.03351 2.95919C6.58062 3.08436 6.42325 3.32365 6.39591 3.75659C6.36341 4.24989 6.291 4.73952 6.22968 5.27922L2.97151 5.51998C2.64938 8.59543 3.03283 11.528 4.15066 14.3649C5.92751 18.8732 9.59351 22.1011 12.7556 23.6178C14.2909 22.6636 15.6813 21.4944 16.9942 20.1514C16.1911 20.5946 15.4486 21.043 14.6729 21.4229C13.8919 21.8058 13.0645 22.0709 12.1594 22.1379C13.5018 20.6535 14.644 19.0551 15.3112 17.1599C15.9783 15.2676 16.282 13.3364 15.8306 11.3344C15.7382 12.9285 15.5158 14.4953 14.8997 15.9796C14.285 17.4603 13.442 18.7893 12.3286 19.9327L12.22 19.8782C12.2518 19.7678 12.2769 19.6551 12.3168 19.5476C12.7992 18.3026 13.0933 17.0104 13.1442 15.677C13.1781 14.8307 13.0255 13.9873 12.6973 13.206C12.616 13.0124 12.5325 12.8202 12.4246 12.5669C13.08 12.1178 13.7671 11.6628 13.8432 10.7557C13.8942 10.1448 13.7828 9.53123 13.5203 8.97682L13.5218 8.97977L13.5225 8.98051ZM8.49784 8.92529C8.83844 8.79275 9.13913 8.64403 9.45461 8.56451C9.83436 8.47026 10.0309 8.29945 10.1195 7.87314C9.14504 8.04248 8.65817 8.23907 8.49784 8.92529Z"
                fill="currentColor"
              />
            </svg>
          </div>
        ))}
      </div>
    </>
  )
}
