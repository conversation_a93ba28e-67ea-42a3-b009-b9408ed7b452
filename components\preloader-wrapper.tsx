"use client"

import { useState, useEffect } from 'react'
import Preloader from './preloader'

interface PreloaderWrapperProps {
  children: React.ReactNode
}

export default function PreloaderWrapper({ children }: PreloaderWrapperProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [showPreloader, setShowPreloader] = useState(true)

  useEffect(() => {
    // Prevent scrolling while preloader is active
    if (showPreloader) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [showPreloader])

  const handlePreloaderComplete = () => {
    setIsLoading(false)
    setShowPreloader(false)
  }

  return (
    <>
      {showPreloader && (
        <Preloader onComplete={handlePreloaderComplete} />
      )}
      <div style={{ 
        opacity: isLoading ? 0 : 1, 
        transition: 'opacity 0.5s ease-in-out',
        pointerEvents: isLoading ? 'none' : 'auto'
      }}>
        {children}
      </div>
    </>
  )
}
