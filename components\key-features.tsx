"use client"

import { useEffect, useState } from "react"
import Vector1Icon from "./svg/sand-icon"
import DiagramIcon from "./svg/diagram-icon"
import JudgeIcon from "./svg/judge-icon"
import StarIcon from "./svg/star-icon"
import MessageIcon from "./svg/message-icon"

export default function KeyFeatures() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const element = document.getElementById('features')
    if (element) {
      observer.observe(element)
    }

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [])

  return (
    <section id="features" className="py-16 md:py-24 relative overflow-hidden">
      {/* Background effects */}
      <div
        className="absolute opacity-20 blur-[120px] parallax-element"
        style={{
          width: "300px",
          height: "300px",
          top: "20%",
          left: "10%",
          background: "radial-gradient(circle, #8C01FA, transparent)",
          zIndex: "0",
          animation: "horseLogoFloat 25s ease-in-out infinite",
        }}
      />
      <div
        className="absolute opacity-20 blur-[120px] parallax-element"
        style={{
          width: "250px",
          height: "250px",
          top: "70%",
          right: "15%",
          background: "radial-gradient(circle, #19FB9B, transparent)",
          zIndex: "0",
          animation: "horseLogoFloat 20s ease-in-out infinite reverse",
        }}
      />

      <div className="container mx-auto text-center relative z-10">
        <h2 className={`text-[28px] md:text-5xl font-bold mb-16 transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`}>
          <span className="gradient-text-animated hover:scale-105 transition-transform duration-300 cursor-default">
            Key Features & Benefits
          </span>
        </h2>

        <div className={`grid grid-cols-1 md:grid-cols-3 gap-[10px] max-w-6xl mx-auto mb-[10px] transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`}>
          <div className="card-3d bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto hover-zone group transition-all duration-300" style={{ animationDelay: '0.1s' }}>
            <div className="card-3d-inner">
              <div className="mb-4">
                <Vector1Icon className="h-8 w-8 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
              </div>
              <h3 className="text-white text-[20px] font-medium mb-2 group-hover:text-[#19FB9B] transition-colors duration-300">Real-Time Pre-Game Predictions</h3>
              <p className="text-white/70 text-[14px] group-hover:text-white transition-colors duration-300">Updated as new team statistics become available.</p>
            </div>
          </div>

          <div className="card-3d bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto hover-zone group transition-all duration-300" style={{ animationDelay: '0.2s' }}>
            <div className="card-3d-inner">
              <div className="mb-4">
                <DiagramIcon className="h-8 w-8 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
              </div>
              <h3 className="text-white text-[20px] font-medium mb-2 group-hover:text-[#19FB9B] transition-colors duration-300">Deep Statistical Analysis</h3>
              <p className="text-white/70 text-[14px] group-hover:text-white transition-colors duration-300">
                Trained on historical player performance, injuries, and matchup dynamics.
              </p>
            </div>
          </div>

          <div className="card-3d bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto hover-zone group transition-all duration-300" style={{ animationDelay: '0.3s' }}>
            <div className="card-3d-inner">
              <div className="mb-4">
                <JudgeIcon className="h-8 w-8 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
              </div>
              <h3 className="text-white text-[20px] font-medium mb-2 group-hover:text-[#19FB9B] transition-colors duration-300">No Bookmaker Bias</h3>
              <p className="text-white/70 text-[14px] group-hover:text-white transition-colors duration-300">Independent AI sports predictions with no agenda.</p>
            </div>
          </div>
        </div>

        <div className={`grid grid-cols-1 md:grid-cols-2 gap-[10px] max-w-[810px] mx-auto mb-12 transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`} style={{ animationDelay: '0.4s' }}>
          <div className="card-3d bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto hover-zone group transition-all duration-300">
            <div className="card-3d-inner">
              <div className="mb-4">
                <StarIcon className="h-8 w-8 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
              </div>
              <h3 className="text-white text-[20px] font-medium mb-2 group-hover:text-[#19FB9B] transition-colors duration-300">Confidence Ratings</h3>
              <p className="text-white/70 text-[14px] group-hover:text-white transition-colors duration-300">Know exactly how confident our AI is in each prediction.</p>
            </div>
          </div>

          <div className="card-3d bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto hover-zone group transition-all duration-300">
            <div className="card-3d-inner">
              <div className="mb-4">
                <MessageIcon className="h-8 w-8 transition-all duration-300 group-hover:scale-110 group-hover:text-[#19FB9B]" />
              </div>
              <h3 className="text-white text-[20px] font-medium mb-2 group-hover:text-[#19FB9B] transition-colors duration-300">Integrated AI Bot</h3>
              <p className="text-white/70 text-[14px] group-hover:text-white transition-colors duration-300">Ask questions about any matchup or prediction.</p>
            </div>
          </div>
        </div>

        <div className={`flex flex-col sm:flex-row items-center justify-center gap-4 w-full transition-all duration-700 ${isVisible ? 'slide-in-bottom' : 'opacity-0'}`} style={{ animationDelay: '0.6s' }}>
          <button className="magnetic-button liquid-button prediction-button text-white text-[16px] sm:text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[56px] sm:h-[48px] flex items-center justify-center relative overflow-hidden group">
            <span className="relative z-10">Get AI Predictions</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
          <button className="magnetic-button border border-white/20 bg-transparent text-white text-[16px] sm:text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[56px] sm:h-[48px] flex items-center justify-center hover:bg-white/10 hover:border-[#19FB9B]/50 transition-all duration-300 relative overflow-hidden group">
            <span className="relative z-10">Sign Up for Exclusive Access</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#19FB9B]/10 to-[#8C01FA]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
      </div>
    </section>
  )
}





