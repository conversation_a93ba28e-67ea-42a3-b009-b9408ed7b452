"use client"

import { useEffect, useState } from 'react'
import Image from 'next/image'

interface PreloaderProps {
  onComplete: () => void
}

export default function Preloader({ onComplete }: PreloaderProps) {
  const [progress, setProgress] = useState(0)
  const [stage, setStage] = useState<'loading' | 'complete' | 'exit'>('loading')

  useEffect(() => {
    // Simulate loading progress
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setStage('complete')
          // Start exit animation after a brief pause
          setTimeout(() => {
            setStage('exit')
            // Complete the preloader after exit animation
            setTimeout(() => {
              onComplete()
            }, 800)
          }, 500)
          return 100
        }
        // Realistic loading curve - faster at start, slower near end
        const increment = prev < 50 ? Math.random() * 8 + 2 : 
                         prev < 80 ? Math.random() * 4 + 1 : 
                         Math.random() * 2 + 0.5
        return Math.min(prev + increment, 100)
      })
    }, 100)

    return () => clearInterval(interval)
  }, [onComplete])

  return (
    <div className={`preloader-container ${stage === 'exit' ? 'preloader-exit' : ''}`}>
      {/* Animated background with particles */}
      <div className="preloader-bg">
        {/* Floating particles */}
        {Array.from({ length: 50 }).map((_, i) => (
          <div
            key={i}
            className="preloader-particle"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          />
        ))}
        
        {/* Gradient orbs */}
        <div className="preloader-orb preloader-orb-1" />
        <div className="preloader-orb preloader-orb-2" />
        <div className="preloader-orb preloader-orb-3" />
      </div>

      {/* Main content */}
      <div className="preloader-content">
        {/* Logo with epic animations */}
        <div className="preloader-logo-container">
          {/* Rotating rings */}
          <div className="preloader-ring preloader-ring-1" />
          <div className="preloader-ring preloader-ring-2" />
          <div className="preloader-ring preloader-ring-3" />
          
          {/* Pulsing glow */}
          <div className="preloader-glow" />
          
          {/* Main logo */}
          <div className="preloader-logo">
            <Image
              src="/white_horse_icon.svg"
              alt="DarkHorse"
              width={120}
              height={130}
              className="preloader-logo-image"
            />
          </div>
        </div>

        {/* Brand text with typewriter effect */}
        <div className="preloader-text">
          <h1 className="preloader-title">
            <span className="preloader-title-dark">DARK</span>
            <span className="preloader-title-horse">HORSE</span>
          </h1>
          <p className="preloader-subtitle">AI-Powered Sports Predictions</p>
        </div>

        {/* Epic progress bar */}
        <div className="preloader-progress-container">
          <div className="preloader-progress-bg">
            <div 
              className="preloader-progress-fill"
              style={{ width: `${progress}%` }}
            />
            <div className="preloader-progress-glow" />
          </div>
          <div className="preloader-percentage">{Math.round(progress)}%</div>
        </div>

        {/* Loading text with dots animation */}
        <div className="preloader-loading-text">
          {stage === 'loading' && (
            <>
              Loading<span className="preloader-dots">
                <span>.</span><span>.</span><span>.</span>
              </span>
            </>
          )}
          {stage === 'complete' && (
            <span className="preloader-ready">Ready to Win!</span>
          )}
        </div>
      </div>

      {/* Lightning effects */}
      <div className="preloader-lightning preloader-lightning-1" />
      <div className="preloader-lightning preloader-lightning-2" />
    </div>
  )
}
