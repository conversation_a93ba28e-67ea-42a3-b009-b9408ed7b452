"use client"

import Image from "next/image"
import { Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useState } from 'react';
import { useIsMobile } from "@/hooks/use-mobile"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import RightAsideContent from "@/components/right-aside-content"
import CreditsModal from "@/components/credits-modal"
import Link from "next/link"

interface MainHeaderProps {
  onProfileClick: () => void;
}

export default function MainHeader({ onProfileClick }: MainHeaderProps) {
  const isMobile = useIsMobile();
  const [showModal, setShowModal] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [currentView, setCurrentView] = useState<'slip' | 'profile' | 'notifications' | 'settings' | 'help-info' | 'contact' | 'sign-out'>('slip');
  const [showCreditsModal, setShowCreditsModal] = useState(false);
  const [creditsModalTab, setCreditsModalTab] = useState<"first" | "second">("first");

  const handleProfileClick = () => {
    if (isMobile) {
      setShowModal(true);
    }
    setShowProfileMenu(!showProfileMenu);
    setCurrentView('profile');
    onProfileClick();
  };

  const handleOpenCreditsModal = (tab: "first" | "second") => {
    setCreditsModalTab(tab);
    setShowCreditsModal(true);
  };

  return (
    <>
      <header className='flex h-16 w-full items-center justify-between px-4 md:px-8 lg:px-6 bg-[#050816] md:bg-transparent'>
        <Link href="/" className="flex items-center gap-2 2xl:hidden logo-hover">
          <Image
            src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg"
            alt="DarkHorse Icon"
            width={30}
            height={32}
          />
          <Image
            src="/DARKHORSE.svg"
            alt="DarkHorse"
            width={120}
            height={20}
            style={{ width: 'auto', height: 'auto' }}
          />
        </Link>

        <div className="ml-auto flex items-center gap-2 md:gap-4">
          <div
            className="flex items-center gap-2 rounded-full bg-[#7f20ef]/20 px-3 py-1 cursor-pointer credits-hover"
            onClick={() => handleOpenCreditsModal("first")}
          >
            <div className="flex h-5 w-5 md:h-6 md:w-6 items-center justify-center rounded-full bg-[#7f20ef]">
              <Image
                src="/star-first.svg"
                alt="First Star Icon"
                width={20}
                height={20}
                style={{ width: 'auto', height: 'auto' }}
                className="w-5 h-5 md:w-6 md:h-6"
              />
            </div>
            <span className="text-sm md:text-base">10</span>
          </div>

          <div
            className="flex items-center gap-2 rounded-full bg-[#ffd900]/20 px-3 py-1 cursor-pointer credits-hover"
            onClick={() => handleOpenCreditsModal("second")}
          >
            <div className="flex h-5 w-5 md:h-6 md:w-6 items-center justify-center rounded-full bg-[#ffd900]">
              <Image
                src="/star-second.svg"
                alt="Second Star Icon"
                width={20}
                height={20}
                style={{ width: 'auto', height: 'auto' }}
                className="w-5 h-5 md:w-6 md:h-6"
              />
            </div>
            <span className="text-sm md:text-base">50</span>
          </div>

          <div className="relative">
            <div
              className="flex items-center gap-2 bg-[#3131314D] md:rounded-full md:px-3 md:py-1 border-none cursor-pointer profile-hover"
              onClick={handleProfileClick}
            >
              <div className="h-8 w-8 md:h-8 md:w-8 rounded-full bg-gray-300">
                <Image
                  src="/sample-avatar.svg"
                  alt="User Avatar"
                  width={20}
                  height={20}
                  style={{ width: 'auto', height: 'auto' }}
                  className="h-8 w-8 md:h-8 md:w-8 rounded-full"
                />
              </div>
              <div className="hidden items-center gap-1 md:flex">
                <span className="font-inter font-bold text-[14px] leading-[130%] tracking-[-0.01em] text-center uppercase text-[#9CA2B5]">
                  Welcome, Ilya
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {isMobile && (
        <Dialog open={showModal} onOpenChange={setShowModal}>
          <DialogContent className="sm:max-w-[425px] bg-[#08101e] border-none p-6 text-white">
            <div className="relative z-10">
              <RightAsideContent
                currentView={currentView}
                onViewChange={setCurrentView}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}

      <CreditsModal
        isOpen={showCreditsModal}
        onClose={() => setShowCreditsModal(false)}
        initialTab={creditsModalTab}
      />
    </>
  )
}






