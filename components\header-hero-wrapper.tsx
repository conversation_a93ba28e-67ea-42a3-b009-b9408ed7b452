"use client"

import Navbar from "./navbar"
import <PERSON><PERSON><PERSON><PERSON> from "./hero-section"
import OurMission from "./our-mission"
import HowAiWorks from "./how-ai-works"
import HelpCenter from "./help-center"
import FAQAccordion from "./faq-accordion"
import ParticleSystem from "./particle-system"
import { usePathname } from "next/navigation"
import GradientDivider from "./gradient-divider"

export default function HeaderHeroWrapper() {
  const pathname = usePathname()

  return (
    <div className="relative overflow-hidden">
      {/* Enhanced Particle System */}
      <ParticleSystem />

      {/* Enhanced Floating Blurs with more dynamic animations */}
      <div
        className="absolute opacity-30 blur-[150px] parallax-element parallax-slow"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "-100px",
          left: "0px",
          background: "linear-gradient(45deg, #2CAD9C, #19FB9B)",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
          animation: "floatBlur1 12s ease-in-out infinite, gradientWave 20s ease infinite",
        }}
      />

      <div
        className="absolute opacity-30 blur-[150px] parallax-element parallax-fast"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "450px",
          right: "40px",
          background: "linear-gradient(45deg, #7F20EF, #8C01FA)",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
          animation: "floatBlur2 15s ease-in-out infinite, gradientWave 25s ease infinite",
        }}
      />

      {/* Additional floating elements */}
      <div
        className="absolute opacity-20 blur-[100px] parallax-element"
        style={{
          width: "200px",
          height: "200px",
          top: "200px",
          left: "50%",
          background: "radial-gradient(circle, #19FB9B, transparent)",
          transform: "translateX(-50%) rotate(45deg)",
          zIndex: "0",
          animation: "horseLogoFloat 18s ease-in-out infinite",
        }}
      />

      <Navbar />
      {pathname === "/about" ? (
        <>
          <OurMission />
          <GradientDivider />
          <HowAiWorks />
        </>
      ) : pathname === "/support" ? (
        <>
          <HelpCenter />
          <GradientDivider />
          <FAQAccordion />
        </>
      ) : (
        <HeroSection />
      )}
    </div>
  )
}




